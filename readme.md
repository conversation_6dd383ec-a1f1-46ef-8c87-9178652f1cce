# 移动应用界面详细描述提示词
## 界面一（登录界面）
一个简洁的移动应用登录界面，采用灰色背景设计。顶部有一个小型logo标识，中间区域包含两个输入框，分别标记为"用户名"和"密码"，用于用户身份验证。底部有一个蓝色的"登录"按钮，按钮宽度与输入框一致，提供明显的视觉引导。界面底部有一个圆形的Home键图标，符合iOS设计风格。整体设计简约直观，聚焦于用户登录功能。

## 界面二（主菜单界面）
应用的主菜单页面，顶部有logo标识和灰色导航栏。中央区域包含两个主要功能按钮："入库扫描"和"查询出库"，按钮采用白色背景配以黑色文字，布局对称。底部有四个导航选项："入库"、"查询"、"回收"和"报表"，形成完整的底部导航栏。界面设计清晰，功能分区明确，便于用户快速选择所需功能。

## 界面三（入库/查询界面）
一个数据录入和查询界面，顶部有logo和导航栏。中间区域显示表格式数据，包含日期时间信息（显示为2023年10月16日）和相关编号。界面包含搜索筛选功能，用户可以按日期范围查询。底部有一个蓝色按钮"确认"，用于提交或确认操作。底部导航栏与主菜单一致，保持了界面的一致性。整体设计注重数据展示和操作效率。

## 界面四（数据详情界面）
数据详情或查询结果界面，顶部保持一致的logo和导航设计。中央区域展示更详细的数据表格，包含多条记录，每条记录显示编号和日期时间信息（2023年10月16日）。界面右侧有操作按钮，标记为"查看"，用蓝色高亮显示。底部显示一串数字编码，可能是系统识别码或批次号。导航栏保持一致，确保用户可以轻松在不同功能间切换。

## 界面五（表单界面）
一个详细的数据录入表单界面，顶部有logo和导航栏。中央区域包含多个输入字段，包括"编号"、"状态"、"入库日期"等多项数据录入选项。每个字段都有清晰的标签和对应的输入框。底部有"确认"和"取消"两个按钮，分别用蓝色和灰色标识，提供明确的操作选择。整体布局紧凑但有序，适合专业用户进行数据录入和管理操作。

所有界面都采用统一android设计语言，包括相似的顶部导航、底部功能栏和iOS风格的设备框架，形成了一个连贯的用户体验。应用整体似乎是一个仓库管理或物流跟踪系统，专注于入库、查询和数据管理功能。界面要求简洁、清晰、易用，以提高用户操作效率和数据管理准确性。

