#Mon May 26 20:43:19 GMT+08:00 2025
com.example.warehousemanagement.app-main-6\:/mipmap-mdpi/ic_launcher_round.png=C\:\\Users\\mw808\\Desktop\\AndroidBox\\app\\build\\intermediates\\packaged_res\\debug\\mipmap-mdpi-v4\\ic_launcher_round.png
com.example.warehousemanagement.app-packageDebugResources-3\:/layout/item_inventory.xml=C\:\\Users\\mw808\\Desktop\\AndroidBox\\app\\build\\intermediates\\packaged_res\\debug\\layout\\item_inventory.xml
com.example.warehousemanagement.app-main-6\:/mipmap-hdpi/ic_launcher_round.png=C\:\\Users\\mw808\\Desktop\\AndroidBox\\app\\build\\intermediates\\packaged_res\\debug\\mipmap-hdpi-v4\\ic_launcher_round.png
com.example.warehousemanagement.app-packageDebugResources-3\:/layout/activity_inventory.xml=C\:\\Users\\mw808\\Desktop\\AndroidBox\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_inventory.xml
com.example.warehousemanagement.app-main-6\:/drawable/ic_query.xml=C\:\\Users\\mw808\\Desktop\\AndroidBox\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_query.xml
com.example.warehousemanagement.app-packageDebugResources-3\:/layout/activity_main.xml=C\:\\Users\\mw808\\Desktop\\AndroidBox\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_main.xml
com.example.warehousemanagement.app-packageDebugResources-3\:/layout/item_detail.xml=C\:\\Users\\mw808\\Desktop\\AndroidBox\\app\\build\\intermediates\\packaged_res\\debug\\layout\\item_detail.xml
com.example.warehousemanagement.app-main-6\:/drawable/ic_in.xml=C\:\\Users\\mw808\\Desktop\\AndroidBox\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_in.xml
com.example.warehousemanagement.app-main-6\:/drawable/bg_date_selector.xml=C\:\\Users\\mw808\\Desktop\\AndroidBox\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\bg_date_selector.xml
com.example.warehousemanagement.app-packageDebugResources-3\:/layout/activity_details.xml=C\:\\Users\\mw808\\Desktop\\AndroidBox\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_details.xml
com.example.warehousemanagement.app-packageDebugResources-3\:/layout/activity_login.xml=C\:\\Users\\mw808\\Desktop\\AndroidBox\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_login.xml
com.example.warehousemanagement.app-main-6\:/mipmap-mdpi/ic_launcher.png=C\:\\Users\\mw808\\Desktop\\AndroidBox\\app\\build\\intermediates\\packaged_res\\debug\\mipmap-mdpi-v4\\ic_launcher.png
com.example.warehousemanagement.app-main-6\:/drawable/logo.xml=C\:\\Users\\mw808\\Desktop\\AndroidBox\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\logo.xml
com.example.warehousemanagement.app-main-6\:/drawable/ic_search.xml=C\:\\Users\\mw808\\Desktop\\AndroidBox\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_search.xml
com.example.warehousemanagement.app-main-6\:/mipmap-hdpi/ic_launcher.png=C\:\\Users\\mw808\\Desktop\\AndroidBox\\app\\build\\intermediates\\packaged_res\\debug\\mipmap-hdpi-v4\\ic_launcher.png
com.example.warehousemanagement.app-main-6\:/drawable/ic_report.xml=C\:\\Users\\mw808\\Desktop\\AndroidBox\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_report.xml
com.example.warehousemanagement.app-main-6\:/drawable/ic_recycle.xml=C\:\\Users\\mw808\\Desktop\\AndroidBox\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_recycle.xml
com.example.warehousemanagement.app-main-6\:/menu/bottom_nav_menu.xml=C\:\\Users\\mw808\\Desktop\\AndroidBox\\app\\build\\intermediates\\packaged_res\\debug\\menu\\bottom_nav_menu.xml
com.example.warehousemanagement.app-packageDebugResources-3\:/layout/activity_form.xml=C\:\\Users\\mw808\\Desktop\\AndroidBox\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_form.xml
