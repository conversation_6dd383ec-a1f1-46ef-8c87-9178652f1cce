<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_detail" modulePackage="com.example.warehousemanagement" filePath="app\src\main\res\layout\item_detail.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.cardview.widget.CardView"><Targets><Target tag="layout/item_detail_0" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="1" startOffset="0" endLine="48" endOffset="35"/></Target><Target id="@+id/tvItemCode" view="TextView"><Expressions/><location startLine="21" startOffset="12" endLine="28" endOffset="42"/></Target><Target id="@+id/tvItemDate" view="TextView"><Expressions/><location startLine="30" startOffset="12" endLine="37" endOffset="41"/></Target><Target id="@+id/btnItemView" view="Button"><Expressions/><location startLine="40" startOffset="8" endLine="46" endOffset="52"/></Target></Targets></Layout>