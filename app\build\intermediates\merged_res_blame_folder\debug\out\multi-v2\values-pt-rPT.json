{"logs": [{"outputFile": "com.example.warehousemanagement.app-mergeDebugResources-48:/values-pt-rPT/values-pt-rPT.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\d9062e4afabf79b168ef61153fb05959\\transformed\\navigation-ui-2.5.3\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,167", "endColumns": "111,119", "endOffsets": "162,282"}, "to": {"startLines": "94,95", "startColumns": "4,4", "startOffsets": "8006,8118", "endColumns": "111,119", "endOffsets": "8113,8233"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\f9b30dd2edbe8ce1c4f414282f3ca90a\\transformed\\appcompat-1.6.1\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,319,426,515,616,734,819,899,991,1085,1182,1276,1375,1469,1565,1660,1752,1844,1929,2036,2147,2249,2357,2465,2572,2737,2836", "endColumns": "107,105,106,88,100,117,84,79,91,93,96,93,98,93,95,94,91,91,84,106,110,101,107,107,106,164,98,85", "endOffsets": "208,314,421,510,611,729,814,894,986,1080,1177,1271,1370,1464,1560,1655,1747,1839,1924,2031,2142,2244,2352,2460,2567,2732,2831,2917"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,97", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "323,431,537,644,733,834,952,1037,1117,1209,1303,1400,1494,1593,1687,1783,1878,1970,2062,2147,2254,2365,2467,2575,2683,2790,2955,8324", "endColumns": "107,105,106,88,100,117,84,79,91,93,96,93,98,93,95,94,91,91,84,106,110,101,107,107,106,164,98,85", "endOffsets": "426,532,639,728,829,947,1032,1112,1204,1298,1395,1489,1588,1682,1778,1873,1965,2057,2142,2249,2360,2462,2570,2678,2785,2950,3049,8405"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\fc452d62a030469530b05e216b4ffb58\\transformed\\core-1.9.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "98", "startColumns": "4", "startOffsets": "8410", "endColumns": "100", "endOffsets": "8506"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\9dffeb24765a1ebfe542bc0afda46de7\\transformed\\material-1.8.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,273,354,434,516,615,711,814,934,1015,1079,1171,1250,1315,1405,1469,1537,1599,1672,1736,1790,1916,1974,2036,2090,2166,2309,2396,2478,2617,2699,2781,2868,2924,2975,3041,3116,3196,3283,3356,3433,3506,3580,3673,3750,3843,3941,4015,4096,4195,4248,4314,4403,4491,4553,4617,4680,4796,4899,5006,5110,5170,5225", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,80,79,81,98,95,102,119,80,63,91,78,64,89,63,67,61,72,63,53,125,57,61,53,75,142,86,81,138,81,81,86,55,50,65,74,79,86,72,76,72,73,92,76,92,97,73,80,98,52,65,88,87,61,63,62,115,102,106,103,59,54,85", "endOffsets": "268,349,429,511,610,706,809,929,1010,1074,1166,1245,1310,1400,1464,1532,1594,1667,1731,1785,1911,1969,2031,2085,2161,2304,2391,2473,2612,2694,2776,2863,2919,2970,3036,3111,3191,3278,3351,3428,3501,3575,3668,3745,3838,3936,4010,4091,4190,4243,4309,4398,4486,4548,4612,4675,4791,4894,5001,5105,5165,5220,5306"}, "to": {"startLines": "2,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,96", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3054,3135,3215,3297,3396,3492,3595,3715,3796,3860,3952,4031,4096,4186,4250,4318,4380,4453,4517,4571,4697,4755,4817,4871,4947,5090,5177,5259,5398,5480,5562,5649,5705,5756,5822,5897,5977,6064,6137,6214,6287,6361,6454,6531,6624,6722,6796,6877,6976,7029,7095,7184,7272,7334,7398,7461,7577,7680,7787,7891,7951,8238", "endLines": "5,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,96", "endColumns": "12,80,79,81,98,95,102,119,80,63,91,78,64,89,63,67,61,72,63,53,125,57,61,53,75,142,86,81,138,81,81,86,55,50,65,74,79,86,72,76,72,73,92,76,92,97,73,80,98,52,65,88,87,61,63,62,115,102,106,103,59,54,85", "endOffsets": "318,3130,3210,3292,3391,3487,3590,3710,3791,3855,3947,4026,4091,4181,4245,4313,4375,4448,4512,4566,4692,4750,4812,4866,4942,5085,5172,5254,5393,5475,5557,5644,5700,5751,5817,5892,5972,6059,6132,6209,6282,6356,6449,6526,6619,6717,6791,6872,6971,7024,7090,7179,7267,7329,7393,7456,7572,7675,7782,7886,7946,8001,8319"}}]}]}