R_DEF: Internal format may change without notice
local
color accent
color background_gray
color black
color button_blue
color button_gray
color divider
color icons
color primary
color primary_dark
color primary_light
color primary_text
color secondary_text
color white
drawable bg_date_selector
drawable ic_in
drawable ic_query
drawable ic_recycle
drawable ic_report
drawable ic_search
drawable logo
id bottomNavigation
id btnCancel
id btnConfirm
id btnItemView
id btnLogin
id btnSearch
id btnView
id cardQueryOut
id cardScanIn
id etCode
id etDate
id etItem1
id etItem2
id etItem3
id etItem4
id etItem5
id etPassword
id etStatus
id etUsername
id ivLogo
id ivToolbarLogo
id linearLayout
id linearLayoutButtons
id navigation_in
id navigation_query
id navigation_recycle
id navigation_report
id recyclerView
id tilCode
id tilDate
id tilItem1
id tilItem2
id tilItem3
id tilItem4
id tilItem5
id tilPassword
id tilStatus
id tilUsername
id toolbar
id tvCode
id tvCodeInfo
id tvDate
id tvDateFrom
id tvDateTo
id tvItemCode
id tvItemDate
layout activity_details
layout activity_form
layout activity_inventory
layout activity_login
layout activity_main
layout item_detail
layout item_inventory
menu bottom_nav_menu
mipmap ic_launcher
mipmap ic_launcher_round
string app_name
string cancel
string code
string code_number
string confirm
string date
string date_format
string date_from
string date_to
string in_date
string item_1
string item_2
string item_3
string item_4
string item_5
string login
string nav_in
string nav_query
string nav_recycle
string nav_report
string password
string password_hint
string query_out
string scan_in
string search
string status
string username
string username_hint
string view
style AppButton
style AppButton.Blue
style AppButton.Gray
style AppTextField
style Theme.WarehouseManagement
style Theme.WarehouseManagement.NoActionBar
