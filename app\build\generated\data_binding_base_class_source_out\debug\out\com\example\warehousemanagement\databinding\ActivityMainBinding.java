// Generated by view binder compiler. Do not edit!
package com.example.warehousemanagement.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.cardview.widget.CardView;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.warehousemanagement.R;
import com.google.android.material.bottomnavigation.BottomNavigationView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityMainBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final BottomNavigationView bottomNavigation;

  @NonNull
  public final CardView cardQueryOut;

  @NonNull
  public final CardView cardScanIn;

  @NonNull
  public final ImageView ivToolbarLogo;

  @NonNull
  public final Toolbar toolbar;

  private ActivityMainBinding(@NonNull ConstraintLayout rootView,
      @NonNull BottomNavigationView bottomNavigation, @NonNull CardView cardQueryOut,
      @NonNull CardView cardScanIn, @NonNull ImageView ivToolbarLogo, @NonNull Toolbar toolbar) {
    this.rootView = rootView;
    this.bottomNavigation = bottomNavigation;
    this.cardQueryOut = cardQueryOut;
    this.cardScanIn = cardScanIn;
    this.ivToolbarLogo = ivToolbarLogo;
    this.toolbar = toolbar;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityMainBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityMainBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_main, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityMainBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.bottomNavigation;
      BottomNavigationView bottomNavigation = ViewBindings.findChildViewById(rootView, id);
      if (bottomNavigation == null) {
        break missingId;
      }

      id = R.id.cardQueryOut;
      CardView cardQueryOut = ViewBindings.findChildViewById(rootView, id);
      if (cardQueryOut == null) {
        break missingId;
      }

      id = R.id.cardScanIn;
      CardView cardScanIn = ViewBindings.findChildViewById(rootView, id);
      if (cardScanIn == null) {
        break missingId;
      }

      id = R.id.ivToolbarLogo;
      ImageView ivToolbarLogo = ViewBindings.findChildViewById(rootView, id);
      if (ivToolbarLogo == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      return new ActivityMainBinding((ConstraintLayout) rootView, bottomNavigation, cardQueryOut,
          cardScanIn, ivToolbarLogo, toolbar);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
