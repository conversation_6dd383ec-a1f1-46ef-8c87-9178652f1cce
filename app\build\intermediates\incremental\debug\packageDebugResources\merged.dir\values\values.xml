<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="accent">#448AFF</color>
    <color name="background_gray">#F5F5F5</color>
    <color name="black">#000000</color>
    <color name="button_blue">#2196F3</color>
    <color name="button_gray">#9E9E9E</color>
    <color name="divider">#BDBDBD</color>
    <color name="icons">#FFFFFF</color>
    <color name="primary">#3F51B5</color>
    <color name="primary_dark">#303F9F</color>
    <color name="primary_light">#C5CAE9</color>
    <color name="primary_text">#212121</color>
    <color name="secondary_text">#757575</color>
    <color name="white">#FFFFFF</color>
    <string name="app_name">仓库管理系统</string>
    <string name="cancel">取消</string>
    <string name="code">编码</string>
    <string name="code_number">编号</string>
    <string name="confirm">确认</string>
    <string name="date">日期</string>
    <string name="date_format">yyyy年MM月dd日</string>
    <string name="date_from">开始日期</string>
    <string name="date_to">结束日期</string>
    <string name="in_date">入库日期</string>
    <string name="item_1">条目1</string>
    <string name="item_2">条目2</string>
    <string name="item_3">条目3</string>
    <string name="item_4">条目4</string>
    <string name="item_5">条目5</string>
    <string name="login">登录</string>
    <string name="nav_in">入库</string>
    <string name="nav_query">查询</string>
    <string name="nav_recycle">回收</string>
    <string name="nav_report">报表</string>
    <string name="password">密码</string>
    <string name="password_hint">请输入密码</string>
    <string name="query_out">查询出库</string>
    <string name="scan_in">入库扫描</string>
    <string name="search">搜索</string>
    <string name="status">状态</string>
    <string name="username">用户名</string>
    <string name="username_hint">请输入用户名</string>
    <string name="view">查看</string>
    <style name="AppButton" parent="Widget.MaterialComponents.Button">
        <item name="android:textColor">@color/white</item>
        <item name="android:padding">12dp</item>
        <item name="android:textSize">16sp</item>
    </style>
    <style name="AppButton.Blue">
        <item name="backgroundTint">@color/button_blue</item>
    </style>
    <style name="AppButton.Gray">
        <item name="backgroundTint">@color/button_gray</item>
    </style>
    <style name="AppTextField" parent="Widget.MaterialComponents.TextInputLayout.OutlinedBox">
        <item name="boxStrokeColor">@color/primary</item>
        <item name="hintTextColor">@color/primary</item>
    </style>
    <style name="Theme.WarehouseManagement" parent="Theme.MaterialComponents.Light.DarkActionBar">
        
        <item name="colorPrimary">@color/primary</item>
        <item name="colorPrimaryVariant">@color/primary_dark</item>
        <item name="colorOnPrimary">@color/white</item>
        
        <item name="colorSecondary">@color/accent</item>
        <item name="colorSecondaryVariant">@color/accent</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <item name="android:statusBarColor">@color/primary_dark</item>
        
        <item name="android:windowBackground">@color/background_gray</item>
    </style>
    <style name="Theme.WarehouseManagement.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
</resources>