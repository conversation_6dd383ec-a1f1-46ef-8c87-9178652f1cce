<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_main" modulePackage="com.example.warehousemanagement" filePath="app\src\main\res\layout\activity_main.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/activity_main_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="91" endOffset="51"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="8" startOffset="4" endLine="24" endOffset="39"/></Target><Target id="@+id/ivToolbarLogo" view="ImageView"><Expressions/><location startLine="17" startOffset="8" endLine="23" endOffset="42"/></Target><Target id="@+id/cardScanIn" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="36" startOffset="8" endLine="55" endOffset="43"/></Target><Target id="@+id/cardQueryOut" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="57" startOffset="8" endLine="76" endOffset="43"/></Target><Target id="@+id/bottomNavigation" view="com.google.android.material.bottomnavigation.BottomNavigationView"><Expressions/><location startLine="79" startOffset="4" endLine="89" endOffset="42"/></Target></Targets></Layout>