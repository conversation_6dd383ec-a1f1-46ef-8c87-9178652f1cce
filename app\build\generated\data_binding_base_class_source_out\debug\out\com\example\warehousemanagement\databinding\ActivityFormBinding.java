// Generated by view binder compiler. Do not edit!
package com.example.warehousemanagement.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.warehousemanagement.R;
import com.google.android.material.bottomnavigation.BottomNavigationView;
import com.google.android.material.textfield.TextInputEditText;
import com.google.android.material.textfield.TextInputLayout;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityFormBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final BottomNavigationView bottomNavigation;

  @NonNull
  public final Button btnCancel;

  @NonNull
  public final Button btnConfirm;

  @NonNull
  public final TextInputEditText etCode;

  @NonNull
  public final TextInputEditText etDate;

  @NonNull
  public final TextInputEditText etItem1;

  @NonNull
  public final TextInputEditText etItem2;

  @NonNull
  public final TextInputEditText etItem3;

  @NonNull
  public final TextInputEditText etItem4;

  @NonNull
  public final TextInputEditText etItem5;

  @NonNull
  public final TextInputEditText etStatus;

  @NonNull
  public final ImageView ivToolbarLogo;

  @NonNull
  public final LinearLayout linearLayoutButtons;

  @NonNull
  public final TextInputLayout tilCode;

  @NonNull
  public final TextInputLayout tilDate;

  @NonNull
  public final TextInputLayout tilItem1;

  @NonNull
  public final TextInputLayout tilItem2;

  @NonNull
  public final TextInputLayout tilItem3;

  @NonNull
  public final TextInputLayout tilItem4;

  @NonNull
  public final TextInputLayout tilItem5;

  @NonNull
  public final TextInputLayout tilStatus;

  @NonNull
  public final Toolbar toolbar;

  private ActivityFormBinding(@NonNull ConstraintLayout rootView,
      @NonNull BottomNavigationView bottomNavigation, @NonNull Button btnCancel,
      @NonNull Button btnConfirm, @NonNull TextInputEditText etCode,
      @NonNull TextInputEditText etDate, @NonNull TextInputEditText etItem1,
      @NonNull TextInputEditText etItem2, @NonNull TextInputEditText etItem3,
      @NonNull TextInputEditText etItem4, @NonNull TextInputEditText etItem5,
      @NonNull TextInputEditText etStatus, @NonNull ImageView ivToolbarLogo,
      @NonNull LinearLayout linearLayoutButtons, @NonNull TextInputLayout tilCode,
      @NonNull TextInputLayout tilDate, @NonNull TextInputLayout tilItem1,
      @NonNull TextInputLayout tilItem2, @NonNull TextInputLayout tilItem3,
      @NonNull TextInputLayout tilItem4, @NonNull TextInputLayout tilItem5,
      @NonNull TextInputLayout tilStatus, @NonNull Toolbar toolbar) {
    this.rootView = rootView;
    this.bottomNavigation = bottomNavigation;
    this.btnCancel = btnCancel;
    this.btnConfirm = btnConfirm;
    this.etCode = etCode;
    this.etDate = etDate;
    this.etItem1 = etItem1;
    this.etItem2 = etItem2;
    this.etItem3 = etItem3;
    this.etItem4 = etItem4;
    this.etItem5 = etItem5;
    this.etStatus = etStatus;
    this.ivToolbarLogo = ivToolbarLogo;
    this.linearLayoutButtons = linearLayoutButtons;
    this.tilCode = tilCode;
    this.tilDate = tilDate;
    this.tilItem1 = tilItem1;
    this.tilItem2 = tilItem2;
    this.tilItem3 = tilItem3;
    this.tilItem4 = tilItem4;
    this.tilItem5 = tilItem5;
    this.tilStatus = tilStatus;
    this.toolbar = toolbar;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityFormBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityFormBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_form, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityFormBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.bottomNavigation;
      BottomNavigationView bottomNavigation = ViewBindings.findChildViewById(rootView, id);
      if (bottomNavigation == null) {
        break missingId;
      }

      id = R.id.btnCancel;
      Button btnCancel = ViewBindings.findChildViewById(rootView, id);
      if (btnCancel == null) {
        break missingId;
      }

      id = R.id.btnConfirm;
      Button btnConfirm = ViewBindings.findChildViewById(rootView, id);
      if (btnConfirm == null) {
        break missingId;
      }

      id = R.id.etCode;
      TextInputEditText etCode = ViewBindings.findChildViewById(rootView, id);
      if (etCode == null) {
        break missingId;
      }

      id = R.id.etDate;
      TextInputEditText etDate = ViewBindings.findChildViewById(rootView, id);
      if (etDate == null) {
        break missingId;
      }

      id = R.id.etItem1;
      TextInputEditText etItem1 = ViewBindings.findChildViewById(rootView, id);
      if (etItem1 == null) {
        break missingId;
      }

      id = R.id.etItem2;
      TextInputEditText etItem2 = ViewBindings.findChildViewById(rootView, id);
      if (etItem2 == null) {
        break missingId;
      }

      id = R.id.etItem3;
      TextInputEditText etItem3 = ViewBindings.findChildViewById(rootView, id);
      if (etItem3 == null) {
        break missingId;
      }

      id = R.id.etItem4;
      TextInputEditText etItem4 = ViewBindings.findChildViewById(rootView, id);
      if (etItem4 == null) {
        break missingId;
      }

      id = R.id.etItem5;
      TextInputEditText etItem5 = ViewBindings.findChildViewById(rootView, id);
      if (etItem5 == null) {
        break missingId;
      }

      id = R.id.etStatus;
      TextInputEditText etStatus = ViewBindings.findChildViewById(rootView, id);
      if (etStatus == null) {
        break missingId;
      }

      id = R.id.ivToolbarLogo;
      ImageView ivToolbarLogo = ViewBindings.findChildViewById(rootView, id);
      if (ivToolbarLogo == null) {
        break missingId;
      }

      id = R.id.linearLayoutButtons;
      LinearLayout linearLayoutButtons = ViewBindings.findChildViewById(rootView, id);
      if (linearLayoutButtons == null) {
        break missingId;
      }

      id = R.id.tilCode;
      TextInputLayout tilCode = ViewBindings.findChildViewById(rootView, id);
      if (tilCode == null) {
        break missingId;
      }

      id = R.id.tilDate;
      TextInputLayout tilDate = ViewBindings.findChildViewById(rootView, id);
      if (tilDate == null) {
        break missingId;
      }

      id = R.id.tilItem1;
      TextInputLayout tilItem1 = ViewBindings.findChildViewById(rootView, id);
      if (tilItem1 == null) {
        break missingId;
      }

      id = R.id.tilItem2;
      TextInputLayout tilItem2 = ViewBindings.findChildViewById(rootView, id);
      if (tilItem2 == null) {
        break missingId;
      }

      id = R.id.tilItem3;
      TextInputLayout tilItem3 = ViewBindings.findChildViewById(rootView, id);
      if (tilItem3 == null) {
        break missingId;
      }

      id = R.id.tilItem4;
      TextInputLayout tilItem4 = ViewBindings.findChildViewById(rootView, id);
      if (tilItem4 == null) {
        break missingId;
      }

      id = R.id.tilItem5;
      TextInputLayout tilItem5 = ViewBindings.findChildViewById(rootView, id);
      if (tilItem5 == null) {
        break missingId;
      }

      id = R.id.tilStatus;
      TextInputLayout tilStatus = ViewBindings.findChildViewById(rootView, id);
      if (tilStatus == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      return new ActivityFormBinding((ConstraintLayout) rootView, bottomNavigation, btnCancel,
          btnConfirm, etCode, etDate, etItem1, etItem2, etItem3, etItem4, etItem5, etStatus,
          ivToolbarLogo, linearLayoutButtons, tilCode, tilDate, tilItem1, tilItem2, tilItem3,
          tilItem4, tilItem5, tilStatus, toolbar);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
