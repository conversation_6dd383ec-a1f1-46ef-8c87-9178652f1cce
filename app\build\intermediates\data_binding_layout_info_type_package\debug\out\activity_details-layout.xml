<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_details" modulePackage="com.example.warehousemanagement" filePath="app\src\main\res\layout\activity_details.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/activity_details_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="64" endOffset="51"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="8" startOffset="4" endLine="24" endOffset="39"/></Target><Target id="@+id/ivToolbarLogo" view="ImageView"><Expressions/><location startLine="17" startOffset="8" endLine="23" endOffset="42"/></Target><Target id="@+id/recyclerView" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="26" startOffset="4" endLine="35" endOffset="46"/></Target><Target id="@+id/tvCodeInfo" view="TextView"><Expressions/><location startLine="37" startOffset="4" endLine="50" endOffset="55"/></Target><Target id="@+id/bottomNavigation" view="com.google.android.material.bottomnavigation.BottomNavigationView"><Expressions/><location startLine="52" startOffset="4" endLine="62" endOffset="42"/></Target></Targets></Layout>