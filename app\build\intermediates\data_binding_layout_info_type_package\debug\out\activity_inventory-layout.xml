<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_inventory" modulePackage="com.example.warehousemanagement" filePath="app\src\main\res\layout\activity_inventory.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/activity_inventory_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="122" endOffset="51"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="8" startOffset="4" endLine="24" endOffset="39"/></Target><Target id="@+id/ivToolbarLogo" view="ImageView"><Expressions/><location startLine="17" startOffset="8" endLine="23" endOffset="42"/></Target><Target id="@+id/linearLayout" view="LinearLayout"><Expressions/><location startLine="26" startOffset="4" endLine="84" endOffset="18"/></Target><Target id="@+id/tvDateFrom" view="TextView"><Expressions/><location startLine="43" startOffset="8" endLine="54" endOffset="37"/></Target><Target id="@+id/tvDateTo" view="TextView"><Expressions/><location startLine="63" startOffset="8" endLine="73" endOffset="37"/></Target><Target id="@+id/btnSearch" view="ImageButton"><Expressions/><location startLine="75" startOffset="8" endLine="83" endOffset="47"/></Target><Target id="@+id/recyclerView" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="86" startOffset="4" endLine="95" endOffset="49"/></Target><Target id="@+id/btnConfirm" view="Button"><Expressions/><location startLine="97" startOffset="4" endLine="108" endOffset="55"/></Target><Target id="@+id/bottomNavigation" view="com.google.android.material.bottomnavigation.BottomNavigationView"><Expressions/><location startLine="110" startOffset="4" endLine="120" endOffset="42"/></Target></Targets></Layout>