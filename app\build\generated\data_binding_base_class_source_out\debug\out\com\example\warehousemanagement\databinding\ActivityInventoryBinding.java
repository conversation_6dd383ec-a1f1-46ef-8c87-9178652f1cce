// Generated by view binder compiler. Do not edit!
package com.example.warehousemanagement.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.warehousemanagement.R;
import com.google.android.material.bottomnavigation.BottomNavigationView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityInventoryBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final BottomNavigationView bottomNavigation;

  @NonNull
  public final Button btnConfirm;

  @NonNull
  public final ImageButton btnSearch;

  @NonNull
  public final ImageView ivToolbarLogo;

  @NonNull
  public final LinearLayout linearLayout;

  @NonNull
  public final RecyclerView recyclerView;

  @NonNull
  public final Toolbar toolbar;

  @NonNull
  public final TextView tvDateFrom;

  @NonNull
  public final TextView tvDateTo;

  private ActivityInventoryBinding(@NonNull ConstraintLayout rootView,
      @NonNull BottomNavigationView bottomNavigation, @NonNull Button btnConfirm,
      @NonNull ImageButton btnSearch, @NonNull ImageView ivToolbarLogo,
      @NonNull LinearLayout linearLayout, @NonNull RecyclerView recyclerView,
      @NonNull Toolbar toolbar, @NonNull TextView tvDateFrom, @NonNull TextView tvDateTo) {
    this.rootView = rootView;
    this.bottomNavigation = bottomNavigation;
    this.btnConfirm = btnConfirm;
    this.btnSearch = btnSearch;
    this.ivToolbarLogo = ivToolbarLogo;
    this.linearLayout = linearLayout;
    this.recyclerView = recyclerView;
    this.toolbar = toolbar;
    this.tvDateFrom = tvDateFrom;
    this.tvDateTo = tvDateTo;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityInventoryBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityInventoryBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_inventory, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityInventoryBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.bottomNavigation;
      BottomNavigationView bottomNavigation = ViewBindings.findChildViewById(rootView, id);
      if (bottomNavigation == null) {
        break missingId;
      }

      id = R.id.btnConfirm;
      Button btnConfirm = ViewBindings.findChildViewById(rootView, id);
      if (btnConfirm == null) {
        break missingId;
      }

      id = R.id.btnSearch;
      ImageButton btnSearch = ViewBindings.findChildViewById(rootView, id);
      if (btnSearch == null) {
        break missingId;
      }

      id = R.id.ivToolbarLogo;
      ImageView ivToolbarLogo = ViewBindings.findChildViewById(rootView, id);
      if (ivToolbarLogo == null) {
        break missingId;
      }

      id = R.id.linearLayout;
      LinearLayout linearLayout = ViewBindings.findChildViewById(rootView, id);
      if (linearLayout == null) {
        break missingId;
      }

      id = R.id.recyclerView;
      RecyclerView recyclerView = ViewBindings.findChildViewById(rootView, id);
      if (recyclerView == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      id = R.id.tvDateFrom;
      TextView tvDateFrom = ViewBindings.findChildViewById(rootView, id);
      if (tvDateFrom == null) {
        break missingId;
      }

      id = R.id.tvDateTo;
      TextView tvDateTo = ViewBindings.findChildViewById(rootView, id);
      if (tvDateTo == null) {
        break missingId;
      }

      return new ActivityInventoryBinding((ConstraintLayout) rootView, bottomNavigation, btnConfirm,
          btnSearch, ivToolbarLogo, linearLayout, recyclerView, toolbar, tvDateFrom, tvDateTo);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
