[{"merged": "com.example.warehousemanagement.app-mergeDebugResources-49:/layout/activity_login.xml", "source": "com.example.warehousemanagement.app-main-52:/layout/activity_login.xml"}, {"merged": "com.example.warehousemanagement.app-mergeDebugResources-49:/layout/item_inventory.xml", "source": "com.example.warehousemanagement.app-main-52:/layout/item_inventory.xml"}, {"merged": "com.example.warehousemanagement.app-mergeDebugResources-49:/layout/item_detail.xml", "source": "com.example.warehousemanagement.app-main-52:/layout/item_detail.xml"}, {"merged": "com.example.warehousemanagement.app-mergeDebugResources-49:/layout/activity_details.xml", "source": "com.example.warehousemanagement.app-main-52:/layout/activity_details.xml"}, {"merged": "com.example.warehousemanagement.app-mergeDebugResources-49:/layout/activity_form.xml", "source": "com.example.warehousemanagement.app-main-52:/layout/activity_form.xml"}, {"merged": "com.example.warehousemanagement.app-mergeDebugResources-49:/layout/activity_main.xml", "source": "com.example.warehousemanagement.app-main-52:/layout/activity_main.xml"}, {"merged": "com.example.warehousemanagement.app-mergeDebugResources-49:/layout/activity_inventory.xml", "source": "com.example.warehousemanagement.app-main-52:/layout/activity_inventory.xml"}]