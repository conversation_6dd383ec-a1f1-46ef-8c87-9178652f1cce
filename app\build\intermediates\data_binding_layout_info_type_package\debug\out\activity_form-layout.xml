<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_form" modulePackage="com.example.warehousemanagement" filePath="app\src\main\res\layout\activity_form.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/activity_form_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="206" endOffset="51"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="8" startOffset="4" endLine="24" endOffset="39"/></Target><Target id="@+id/ivToolbarLogo" view="ImageView"><Expressions/><location startLine="17" startOffset="8" endLine="23" endOffset="42"/></Target><Target id="@+id/tilCode" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="40" startOffset="12" endLine="53" endOffset="67"/></Target><Target id="@+id/etCode" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="48" startOffset="16" endLine="52" endOffset="46"/></Target><Target id="@+id/tilStatus" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="55" startOffset="12" endLine="68" endOffset="67"/></Target><Target id="@+id/etStatus" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="63" startOffset="16" endLine="67" endOffset="46"/></Target><Target id="@+id/tilDate" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="70" startOffset="12" endLine="86" endOffset="67"/></Target><Target id="@+id/etDate" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="78" startOffset="16" endLine="85" endOffset="58"/></Target><Target id="@+id/tilItem1" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="88" startOffset="12" endLine="101" endOffset="67"/></Target><Target id="@+id/etItem1" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="96" startOffset="16" endLine="100" endOffset="46"/></Target><Target id="@+id/tilItem2" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="103" startOffset="12" endLine="116" endOffset="67"/></Target><Target id="@+id/etItem2" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="111" startOffset="16" endLine="115" endOffset="46"/></Target><Target id="@+id/tilItem3" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="118" startOffset="12" endLine="131" endOffset="67"/></Target><Target id="@+id/etItem3" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="126" startOffset="16" endLine="130" endOffset="46"/></Target><Target id="@+id/tilItem4" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="133" startOffset="12" endLine="146" endOffset="67"/></Target><Target id="@+id/etItem4" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="141" startOffset="16" endLine="145" endOffset="46"/></Target><Target id="@+id/tilItem5" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="148" startOffset="12" endLine="161" endOffset="67"/></Target><Target id="@+id/etItem5" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="156" startOffset="16" endLine="160" endOffset="46"/></Target><Target id="@+id/linearLayoutButtons" view="LinearLayout"><Expressions/><location startLine="165" startOffset="4" endLine="192" endOffset="18"/></Target><Target id="@+id/btnCancel" view="Button"><Expressions/><location startLine="175" startOffset="8" endLine="182" endOffset="43"/></Target><Target id="@+id/btnConfirm" view="Button"><Expressions/><location startLine="184" startOffset="8" endLine="191" endOffset="44"/></Target><Target id="@+id/bottomNavigation" view="com.google.android.material.bottomnavigation.BottomNavigationView"><Expressions/><location startLine="194" startOffset="4" endLine="204" endOffset="42"/></Target></Targets></Layout>