1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.warehousemanagement"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
8-->C:\Users\<USER>\Desktop\AndroidBox\app\src\main\AndroidManifest.xml
9        android:targetSdkVersion="33" />
9-->C:\Users\<USER>\Desktop\AndroidBox\app\src\main\AndroidManifest.xml
10
11    <permission
11-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fc452d62a030469530b05e216b4ffb58\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
12        android:name="com.example.warehousemanagement.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
12-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fc452d62a030469530b05e216b4ffb58\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
13        android:protectionLevel="signature" />
13-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fc452d62a030469530b05e216b4ffb58\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
14
15    <uses-permission android:name="com.example.warehousemanagement.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
15-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fc452d62a030469530b05e216b4ffb58\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
15-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fc452d62a030469530b05e216b4ffb58\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
16
17    <application
17-->C:\Users\<USER>\Desktop\AndroidBox\app\src\main\AndroidManifest.xml:5:5-24:19
18        android:allowBackup="true"
18-->C:\Users\<USER>\Desktop\AndroidBox\app\src\main\AndroidManifest.xml:6:9-35
19        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
19-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fc452d62a030469530b05e216b4ffb58\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
20        android:debuggable="true"
21        android:icon="@mipmap/ic_launcher"
21-->C:\Users\<USER>\Desktop\AndroidBox\app\src\main\AndroidManifest.xml:7:9-43
22        android:label="@string/app_name"
22-->C:\Users\<USER>\Desktop\AndroidBox\app\src\main\AndroidManifest.xml:8:9-41
23        android:roundIcon="@mipmap/ic_launcher_round"
23-->C:\Users\<USER>\Desktop\AndroidBox\app\src\main\AndroidManifest.xml:9:9-54
24        android:supportsRtl="true"
24-->C:\Users\<USER>\Desktop\AndroidBox\app\src\main\AndroidManifest.xml:10:9-35
25        android:testOnly="true"
26        android:theme="@style/Theme.WarehouseManagement" >
26-->C:\Users\<USER>\Desktop\AndroidBox\app\src\main\AndroidManifest.xml:11:9-57
27        <activity
27-->C:\Users\<USER>\Desktop\AndroidBox\app\src\main\AndroidManifest.xml:12:9-19:20
28            android:name="com.example.warehousemanagement.ui.login.LoginActivity"
28-->C:\Users\<USER>\Desktop\AndroidBox\app\src\main\AndroidManifest.xml:13:13-51
29            android:exported="true" >
29-->C:\Users\<USER>\Desktop\AndroidBox\app\src\main\AndroidManifest.xml:14:13-36
30            <intent-filter>
30-->C:\Users\<USER>\Desktop\AndroidBox\app\src\main\AndroidManifest.xml:15:13-18:29
31                <action android:name="android.intent.action.MAIN" />
31-->C:\Users\<USER>\Desktop\AndroidBox\app\src\main\AndroidManifest.xml:16:17-69
31-->C:\Users\<USER>\Desktop\AndroidBox\app\src\main\AndroidManifest.xml:16:25-66
32
33                <category android:name="android.intent.category.LAUNCHER" />
33-->C:\Users\<USER>\Desktop\AndroidBox\app\src\main\AndroidManifest.xml:17:17-77
33-->C:\Users\<USER>\Desktop\AndroidBox\app\src\main\AndroidManifest.xml:17:27-74
34            </intent-filter>
35        </activity>
36        <activity android:name="com.example.warehousemanagement.ui.main.MainActivity" />
36-->C:\Users\<USER>\Desktop\AndroidBox\app\src\main\AndroidManifest.xml:20:9-58
36-->C:\Users\<USER>\Desktop\AndroidBox\app\src\main\AndroidManifest.xml:20:19-55
37        <activity android:name="com.example.warehousemanagement.ui.inventory.InventoryActivity" />
37-->C:\Users\<USER>\Desktop\AndroidBox\app\src\main\AndroidManifest.xml:21:9-68
37-->C:\Users\<USER>\Desktop\AndroidBox\app\src\main\AndroidManifest.xml:21:19-65
38        <activity android:name="com.example.warehousemanagement.ui.details.DetailsActivity" />
38-->C:\Users\<USER>\Desktop\AndroidBox\app\src\main\AndroidManifest.xml:22:9-64
38-->C:\Users\<USER>\Desktop\AndroidBox\app\src\main\AndroidManifest.xml:22:19-61
39        <activity android:name="com.example.warehousemanagement.ui.form.FormActivity" />
39-->C:\Users\<USER>\Desktop\AndroidBox\app\src\main\AndroidManifest.xml:23:9-58
39-->C:\Users\<USER>\Desktop\AndroidBox\app\src\main\AndroidManifest.xml:23:19-55
40
41        <provider
41-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fa1d8ccaa81097404a06405cfd449d10\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
42            android:name="androidx.startup.InitializationProvider"
42-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fa1d8ccaa81097404a06405cfd449d10\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
43            android:authorities="com.example.warehousemanagement.androidx-startup"
43-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fa1d8ccaa81097404a06405cfd449d10\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
44            android:exported="false" >
44-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fa1d8ccaa81097404a06405cfd449d10\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
45            <meta-data
45-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fa1d8ccaa81097404a06405cfd449d10\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
46                android:name="androidx.emoji2.text.EmojiCompatInitializer"
46-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fa1d8ccaa81097404a06405cfd449d10\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
47                android:value="androidx.startup" />
47-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fa1d8ccaa81097404a06405cfd449d10\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
48            <meta-data
48-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\768f5e118860e67832017a2555dd9fd7\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:29:13-31:52
49                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
49-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\768f5e118860e67832017a2555dd9fd7\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:30:17-78
50                android:value="androidx.startup" />
50-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\768f5e118860e67832017a2555dd9fd7\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:31:17-49
51            <meta-data
51-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\379fe34e4f73391c7d8a7c219728f829\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
52                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
52-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\379fe34e4f73391c7d8a7c219728f829\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
53                android:value="androidx.startup" />
53-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\379fe34e4f73391c7d8a7c219728f829\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
54        </provider>
55
56        <uses-library
56-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\bc52e4d2ad810d7813966e8a513ae383\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
57            android:name="androidx.window.extensions"
57-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\bc52e4d2ad810d7813966e8a513ae383\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
58            android:required="false" />
58-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\bc52e4d2ad810d7813966e8a513ae383\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
59        <uses-library
59-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\bc52e4d2ad810d7813966e8a513ae383\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
60            android:name="androidx.window.sidecar"
60-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\bc52e4d2ad810d7813966e8a513ae383\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
61            android:required="false" />
61-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\bc52e4d2ad810d7813966e8a513ae383\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
62
63        <receiver
63-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\379fe34e4f73391c7d8a7c219728f829\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
64            android:name="androidx.profileinstaller.ProfileInstallReceiver"
64-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\379fe34e4f73391c7d8a7c219728f829\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
65            android:directBootAware="false"
65-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\379fe34e4f73391c7d8a7c219728f829\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
66            android:enabled="true"
66-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\379fe34e4f73391c7d8a7c219728f829\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
67            android:exported="true"
67-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\379fe34e4f73391c7d8a7c219728f829\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
68            android:permission="android.permission.DUMP" >
68-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\379fe34e4f73391c7d8a7c219728f829\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
69            <intent-filter>
69-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\379fe34e4f73391c7d8a7c219728f829\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
70                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
70-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\379fe34e4f73391c7d8a7c219728f829\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
70-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\379fe34e4f73391c7d8a7c219728f829\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
71            </intent-filter>
72            <intent-filter>
72-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\379fe34e4f73391c7d8a7c219728f829\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
73                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
73-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\379fe34e4f73391c7d8a7c219728f829\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
73-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\379fe34e4f73391c7d8a7c219728f829\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
74            </intent-filter>
75            <intent-filter>
75-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\379fe34e4f73391c7d8a7c219728f829\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
76                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
76-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\379fe34e4f73391c7d8a7c219728f829\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
76-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\379fe34e4f73391c7d8a7c219728f829\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
77            </intent-filter>
78            <intent-filter>
78-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\379fe34e4f73391c7d8a7c219728f829\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
79                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
79-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\379fe34e4f73391c7d8a7c219728f829\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
79-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\379fe34e4f73391c7d8a7c219728f829\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
80            </intent-filter>
81        </receiver>
82    </application>
83
84</manifest>
