// Generated by view binder compiler. Do not edit!
package com.example.warehousemanagement.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.warehousemanagement.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemDetailBinding implements ViewBinding {
  @NonNull
  private final CardView rootView;

  @NonNull
  public final Button btnItemView;

  @NonNull
  public final TextView tvItemCode;

  @NonNull
  public final TextView tvItemDate;

  private ItemDetailBinding(@NonNull CardView rootView, @NonNull Button btnItemView,
      @NonNull TextView tvItemCode, @NonNull TextView tvItemDate) {
    this.rootView = rootView;
    this.btnItemView = btnItemView;
    this.tvItemCode = tvItemCode;
    this.tvItemDate = tvItemDate;
  }

  @Override
  @NonNull
  public CardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemDetailBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemDetailBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_detail, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemDetailBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnItemView;
      Button btnItemView = ViewBindings.findChildViewById(rootView, id);
      if (btnItemView == null) {
        break missingId;
      }

      id = R.id.tvItemCode;
      TextView tvItemCode = ViewBindings.findChildViewById(rootView, id);
      if (tvItemCode == null) {
        break missingId;
      }

      id = R.id.tvItemDate;
      TextView tvItemDate = ViewBindings.findChildViewById(rootView, id);
      if (tvItemDate == null) {
        break missingId;
      }

      return new ItemDetailBinding((CardView) rootView, btnItemView, tvItemCode, tvItemDate);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
