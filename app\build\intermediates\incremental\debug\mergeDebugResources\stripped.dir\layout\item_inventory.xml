<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="4dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="12dp">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tvCode"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="WH20231016001"
                android:textColor="@color/primary_text"
                android:textSize="16sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tvDate"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:text="2023年10月16日"
                android:textColor="@color/secondary_text"
                android:textSize="14sp" />
        </LinearLayout>

        <Button
            android:id="@+id/btnView"
            style="@style/Widget.MaterialComponents.Button.TextButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/view"
            android:textColor="@color/button_blue" />
    </LinearLayout>
</androidx.cardview.widget.CardView>