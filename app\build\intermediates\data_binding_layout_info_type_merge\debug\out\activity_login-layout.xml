<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_login" modulePackage="com.example.warehousemanagement" filePath="app\src\main\res\layout\activity_login.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/activity_login_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="78" endOffset="51"/></Target><Target id="@+id/ivLogo" view="ImageView"><Expressions/><location startLine="9" startOffset="4" endLine="18" endOffset="51"/></Target><Target id="@+id/tilUsername" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="20" startOffset="4" endLine="40" endOffset="59"/></Target><Target id="@+id/etUsername" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="33" startOffset="8" endLine="39" endOffset="50"/></Target><Target id="@+id/tilPassword" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="42" startOffset="4" endLine="63" endOffset="59"/></Target><Target id="@+id/etPassword" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="56" startOffset="8" endLine="62" endOffset="50"/></Target><Target id="@+id/btnLogin" view="Button"><Expressions/><location startLine="65" startOffset="4" endLine="76" endOffset="64"/></Target></Targets></Layout>