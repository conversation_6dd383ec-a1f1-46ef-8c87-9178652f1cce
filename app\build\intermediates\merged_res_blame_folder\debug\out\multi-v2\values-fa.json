{"logs": [{"outputFile": "com.example.warehousemanagement.app-mergeDebugResources-48:/values-fa/values-fa.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\d9062e4afabf79b168ef61153fb05959\\transformed\\navigation-ui-2.5.3\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,164", "endColumns": "108,114", "endOffsets": "159,274"}, "to": {"startLines": "94,95", "startColumns": "4,4", "startOffsets": "7753,7862", "endColumns": "108,114", "endOffsets": "7857,7972"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\fc452d62a030469530b05e216b4ffb58\\transformed\\core-1.9.0\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "98", "startColumns": "4", "startOffsets": "8137", "endColumns": "100", "endOffsets": "8233"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\f9b30dd2edbe8ce1c4f414282f3ca90a\\transformed\\appcompat-1.6.1\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,215,316,427,511,612,727,807,884,977,1072,1164,1258,1360,1455,1552,1646,1739,1829,1911,2019,2123,2221,2327,2432,2537,2694,2795", "endColumns": "109,100,110,83,100,114,79,76,92,94,91,93,101,94,96,93,92,89,81,107,103,97,105,104,104,156,100,81", "endOffsets": "210,311,422,506,607,722,802,879,972,1067,1159,1253,1355,1450,1547,1641,1734,1824,1906,2014,2118,2216,2322,2427,2532,2689,2790,2872"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,97", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "309,419,520,631,715,816,931,1011,1088,1181,1276,1368,1462,1564,1659,1756,1850,1943,2033,2115,2223,2327,2425,2531,2636,2741,2898,8055", "endColumns": "109,100,110,83,100,114,79,76,92,94,91,93,101,94,96,93,92,89,81,107,103,97,105,104,104,156,100,81", "endOffsets": "414,515,626,710,811,926,1006,1083,1176,1271,1363,1457,1559,1654,1751,1845,1938,2028,2110,2218,2322,2420,2526,2631,2736,2893,2994,8132"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\9dffeb24765a1ebfe542bc0afda46de7\\transformed\\material-1.8.0\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,259,334,411,493,586,673,770,899,983,1046,1136,1205,1265,1356,1419,1483,1542,1609,1671,1726,1849,1907,1968,2023,2095,2232,2313,2395,2525,2599,2673,2759,2810,2864,2930,3001,3078,3159,3232,3306,3376,3450,3536,3610,3699,3791,3865,3938,4027,4078,4145,4228,4312,4374,4438,4501,4595,4702,4795,4900,4955,5013", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,74,76,81,92,86,96,128,83,62,89,68,59,90,62,63,58,66,61,54,122,57,60,54,71,136,80,81,129,73,73,85,50,53,65,70,76,80,72,73,69,73,85,73,88,91,73,72,88,50,66,82,83,61,63,62,93,106,92,104,54,57,77", "endOffsets": "254,329,406,488,581,668,765,894,978,1041,1131,1200,1260,1351,1414,1478,1537,1604,1666,1721,1844,1902,1963,2018,2090,2227,2308,2390,2520,2594,2668,2754,2805,2859,2925,2996,3073,3154,3227,3301,3371,3445,3531,3605,3694,3786,3860,3933,4022,4073,4140,4223,4307,4369,4433,4496,4590,4697,4790,4895,4950,5008,5086"}, "to": {"startLines": "2,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,96", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2999,3074,3151,3233,3326,3413,3510,3639,3723,3786,3876,3945,4005,4096,4159,4223,4282,4349,4411,4466,4589,4647,4708,4763,4835,4972,5053,5135,5265,5339,5413,5499,5550,5604,5670,5741,5818,5899,5972,6046,6116,6190,6276,6350,6439,6531,6605,6678,6767,6818,6885,6968,7052,7114,7178,7241,7335,7442,7535,7640,7695,7977", "endLines": "5,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,96", "endColumns": "12,74,76,81,92,86,96,128,83,62,89,68,59,90,62,63,58,66,61,54,122,57,60,54,71,136,80,81,129,73,73,85,50,53,65,70,76,80,72,73,69,73,85,73,88,91,73,72,88,50,66,82,83,61,63,62,93,106,92,104,54,57,77", "endOffsets": "304,3069,3146,3228,3321,3408,3505,3634,3718,3781,3871,3940,4000,4091,4154,4218,4277,4344,4406,4461,4584,4642,4703,4758,4830,4967,5048,5130,5260,5334,5408,5494,5545,5599,5665,5736,5813,5894,5967,6041,6111,6185,6271,6345,6434,6526,6600,6673,6762,6813,6880,6963,7047,7109,7173,7236,7330,7437,7530,7635,7690,7748,8050"}}]}]}