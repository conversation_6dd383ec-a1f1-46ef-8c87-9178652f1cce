com.example.warehousemanagement.app-drawerlayout-1.1.1-0 C:\Users\<USER>\.gradle\caches\8.9\transforms\02accdb698f4128289d1a4ee03cbbe72\transformed\drawerlayout-1.1.1\res
com.example.warehousemanagement.app-annotation-experimental-1.3.0-1 C:\Users\<USER>\.gradle\caches\8.9\transforms\035e5ee53db839820290abb1f5c8abb2\transformed\annotation-experimental-1.3.0\res
com.example.warehousemanagement.app-lifecycle-viewmodel-ktx-2.6.1-2 C:\Users\<USER>\.gradle\caches\8.9\transforms\0d6affcf7fa09b1c387a987bf0e3f069\transformed\lifecycle-viewmodel-ktx-2.6.1\res
com.example.warehousemanagement.app-lifecycle-livedata-core-ktx-2.6.1-3 C:\Users\<USER>\.gradle\caches\8.9\transforms\1e45250c255e399cdf4180b46bb280f7\transformed\lifecycle-livedata-core-ktx-2.6.1\res
com.example.warehousemanagement.app-coordinatorlayout-1.1.0-4 C:\Users\<USER>\.gradle\caches\8.9\transforms\1fab25c56387b6fd3837403f3c5df41d\transformed\coordinatorlayout-1.1.0\res
com.example.warehousemanagement.app-navigation-common-2.5.3-5 C:\Users\<USER>\.gradle\caches\8.9\transforms\21dbc59e112b7a8379e35a764b198581\transformed\navigation-common-2.5.3\res
com.example.warehousemanagement.app-activity-1.6.0-6 C:\Users\<USER>\.gradle\caches\8.9\transforms\21df573544f10accad497b5311620025\transformed\activity-1.6.0\res
com.example.warehousemanagement.app-lifecycle-runtime-2.6.1-7 C:\Users\<USER>\.gradle\caches\8.9\transforms\297de8af40cad3be55693d947f0538d0\transformed\lifecycle-runtime-2.6.1\res
com.example.warehousemanagement.app-emoji2-views-helper-1.2.0-8 C:\Users\<USER>\.gradle\caches\8.9\transforms\2bced6bd5cef315e740fc97d85a0df0e\transformed\emoji2-views-helper-1.2.0\res
com.example.warehousemanagement.app-profileinstaller-1.3.0-9 C:\Users\<USER>\.gradle\caches\8.9\transforms\379fe34e4f73391c7d8a7c219728f829\transformed\profileinstaller-1.3.0\res
com.example.warehousemanagement.app-navigation-runtime-2.5.3-10 C:\Users\<USER>\.gradle\caches\8.9\transforms\4b0cf66cf16dec6f5b0cb7ed8d525724\transformed\navigation-runtime-2.5.3\res
com.example.warehousemanagement.app-navigation-runtime-ktx-2.5.3-11 C:\Users\<USER>\.gradle\caches\8.9\transforms\4bda3057e0c059b679b968eb8db780a9\transformed\navigation-runtime-ktx-2.5.3\res
com.example.warehousemanagement.app-cardview-1.0.0-12 C:\Users\<USER>\.gradle\caches\8.9\transforms\4f3b9ca33fa569ba438d95b768a0cc67\transformed\cardview-1.0.0\res
com.example.warehousemanagement.app-slidingpanelayout-1.2.0-13 C:\Users\<USER>\.gradle\caches\8.9\transforms\53704423ce3d578a9f59946aba913f08\transformed\slidingpanelayout-1.2.0\res
com.example.warehousemanagement.app-lifecycle-livedata-ktx-2.6.1-14 C:\Users\<USER>\.gradle\caches\8.9\transforms\684089fb2337216996bc7d1b40f12628\transformed\lifecycle-livedata-ktx-2.6.1\res
com.example.warehousemanagement.app-activity-ktx-1.6.0-15 C:\Users\<USER>\.gradle\caches\8.9\transforms\6f40771fd8225bd99a630a1116ef7530\transformed\activity-ktx-1.6.0\res
com.example.warehousemanagement.app-customview-poolingcontainer-1.0.0-16 C:\Users\<USER>\.gradle\caches\8.9\transforms\6fa18490298591ade163be400dbc310c\transformed\customview-poolingcontainer-1.0.0\res
com.example.warehousemanagement.app-fragment-ktx-1.5.4-17 C:\Users\<USER>\.gradle\caches\8.9\transforms\70756793f8f874d27b6d9ddb73ced022\transformed\fragment-ktx-1.5.4\res
com.example.warehousemanagement.app-core-runtime-2.2.0-18 C:\Users\<USER>\.gradle\caches\8.9\transforms\724109ea684b7dc05a1545525bbb470d\transformed\core-runtime-2.2.0\res
com.example.warehousemanagement.app-savedstate-ktx-1.2.1-19 C:\Users\<USER>\.gradle\caches\8.9\transforms\7315437858f2bdbc14c2804a5acfc976\transformed\savedstate-ktx-1.2.1\res
com.example.warehousemanagement.app-lifecycle-process-2.6.1-20 C:\Users\<USER>\.gradle\caches\8.9\transforms\768f5e118860e67832017a2555dd9fd7\transformed\lifecycle-process-2.6.1\res
com.example.warehousemanagement.app-startup-runtime-1.1.1-21 C:\Users\<USER>\.gradle\caches\8.9\transforms\79b92b8fdd48ab00fd7d229817722e54\transformed\startup-runtime-1.1.1\res
com.example.warehousemanagement.app-lifecycle-viewmodel-savedstate-2.6.1-22 C:\Users\<USER>\.gradle\caches\8.9\transforms\7b2cf4bf03fc7e3f192f802d13b8a5eb\transformed\lifecycle-viewmodel-savedstate-2.6.1\res
com.example.warehousemanagement.app-lifecycle-livedata-core-2.6.1-23 C:\Users\<USER>\.gradle\caches\8.9\transforms\8b31793b4a0a7c7fd25db0369286e704\transformed\lifecycle-livedata-core-2.6.1\res
com.example.warehousemanagement.app-core-ktx-1.9.0-24 C:\Users\<USER>\.gradle\caches\8.9\transforms\8e3bda142f79ea93521cdebf23f788cb\transformed\core-ktx-1.9.0\res
com.example.warehousemanagement.app-appcompat-resources-1.6.1-25 C:\Users\<USER>\.gradle\caches\8.9\transforms\996c7429f920ee7304b64224a021ac5d\transformed\appcompat-resources-1.6.1\res
com.example.warehousemanagement.app-recyclerview-1.3.0-26 C:\Users\<USER>\.gradle\caches\8.9\transforms\9b4748fdcc3140f1247b35f392144d3a\transformed\recyclerview-1.3.0\res
com.example.warehousemanagement.app-material-1.8.0-27 C:\Users\<USER>\.gradle\caches\8.9\transforms\9dffeb24765a1ebfe542bc0afda46de7\transformed\material-1.8.0\res
com.example.warehousemanagement.app-savedstate-1.2.1-28 C:\Users\<USER>\.gradle\caches\8.9\transforms\a285e83488948f4b39b3617938d5671d\transformed\savedstate-1.2.1\res
com.example.warehousemanagement.app-viewpager2-1.0.0-29 C:\Users\<USER>\.gradle\caches\8.9\transforms\ab45614296a7244bf441d924b7750d79\transformed\viewpager2-1.0.0\res
com.example.warehousemanagement.app-navigation-common-ktx-2.5.3-30 C:\Users\<USER>\.gradle\caches\8.9\transforms\ad7bdd1e7b6db73d537f0ca245d5baa2\transformed\navigation-common-ktx-2.5.3\res
com.example.warehousemanagement.app-window-1.0.0-31 C:\Users\<USER>\.gradle\caches\8.9\transforms\bc52e4d2ad810d7813966e8a513ae383\transformed\window-1.0.0\res
com.example.warehousemanagement.app-transition-1.4.1-32 C:\Users\<USER>\.gradle\caches\8.9\transforms\bdc8223aeea0b737f77c1d87ff10ad0f\transformed\transition-1.4.1\res
com.example.warehousemanagement.app-navigation-ui-ktx-2.5.3-33 C:\Users\<USER>\.gradle\caches\8.9\transforms\c0a07ea3880aa01c745e0c860bcb2a6a\transformed\navigation-ui-ktx-2.5.3\res
com.example.warehousemanagement.app-lifecycle-runtime-ktx-2.6.1-34 C:\Users\<USER>\.gradle\caches\8.9\transforms\d37978d5a161d5bccef96646dac4ce05\transformed\lifecycle-runtime-ktx-2.6.1\res
com.example.warehousemanagement.app-navigation-ui-2.5.3-35 C:\Users\<USER>\.gradle\caches\8.9\transforms\d9062e4afabf79b168ef61153fb05959\transformed\navigation-ui-2.5.3\res
com.example.warehousemanagement.app-navigation-fragment-2.5.3-36 C:\Users\<USER>\.gradle\caches\8.9\transforms\dc1eef3fc37bbaba8f8b4f266094c94d\transformed\navigation-fragment-2.5.3\res
com.example.warehousemanagement.app-navigation-fragment-ktx-2.5.3-37 C:\Users\<USER>\.gradle\caches\8.9\transforms\df87e9e766353adeb24d96b414cd0cba\transformed\navigation-fragment-ktx-2.5.3\res
com.example.warehousemanagement.app-fragment-1.5.4-38 C:\Users\<USER>\.gradle\caches\8.9\transforms\e8fff48293b0c01561a4babcb372bc26\transformed\fragment-1.5.4\res
com.example.warehousemanagement.app-lifecycle-viewmodel-2.6.1-39 C:\Users\<USER>\.gradle\caches\8.9\transforms\eb2214e097cd23e62c2f0660703740d7\transformed\lifecycle-viewmodel-2.6.1\res
com.example.warehousemanagement.app-lifecycle-livedata-2.6.1-40 C:\Users\<USER>\.gradle\caches\8.9\transforms\f4b3bdfc9354760df024794bc20b01ee\transformed\lifecycle-livedata-2.6.1\res
com.example.warehousemanagement.app-appcompat-1.6.1-41 C:\Users\<USER>\.gradle\caches\8.9\transforms\f9b30dd2edbe8ce1c4f414282f3ca90a\transformed\appcompat-1.6.1\res
com.example.warehousemanagement.app-emoji2-1.2.0-42 C:\Users\<USER>\.gradle\caches\8.9\transforms\fa1d8ccaa81097404a06405cfd449d10\transformed\emoji2-1.2.0\res
com.example.warehousemanagement.app-core-1.9.0-43 C:\Users\<USER>\.gradle\caches\8.9\transforms\fc452d62a030469530b05e216b4ffb58\transformed\core-1.9.0\res
com.example.warehousemanagement.app-constraintlayout-2.1.4-44 C:\Users\<USER>\.gradle\caches\8.9\transforms\fe7012fb577e3054cc64a14b48e89600\transformed\constraintlayout-2.1.4\res
com.example.warehousemanagement.app-pngs-45 C:\Users\<USER>\Desktop\AndroidBox\app\build\generated\res\pngs\debug
com.example.warehousemanagement.app-resValues-46 C:\Users\<USER>\Desktop\AndroidBox\app\build\generated\res\resValues\debug
com.example.warehousemanagement.app-rs-47 C:\Users\<USER>\Desktop\AndroidBox\app\build\generated\res\rs\debug
com.example.warehousemanagement.app-packageDebugResources-48 C:\Users\<USER>\Desktop\AndroidBox\app\build\intermediates\incremental\debug\packageDebugResources\merged.dir
com.example.warehousemanagement.app-packageDebugResources-49 C:\Users\<USER>\Desktop\AndroidBox\app\build\intermediates\incremental\debug\packageDebugResources\stripped.dir
com.example.warehousemanagement.app-merged_res-50 C:\Users\<USER>\Desktop\AndroidBox\app\build\intermediates\merged_res\debug
com.example.warehousemanagement.app-debug-51 C:\Users\<USER>\Desktop\AndroidBox\app\src\debug\res
com.example.warehousemanagement.app-main-52 C:\Users\<USER>\Desktop\AndroidBox\app\src\main\res
