{"logs": [{"outputFile": "com.example.warehousemanagement.app-mergeDebugResources-48:/values-km/values-km.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\9dffeb24765a1ebfe542bc0afda46de7\\transformed\\material-1.8.0\\res\\values-km\\values-km.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,269,347,423,503,582,661,761,873,953,1018,1112,1182,1244,1331,1394,1459,1518,1583,1644,1701,1820,1878,1939,1996,2067,2197,2283,2361,2499,2574,2645,2742,2797,2853,2919,2999,3089,3175,3254,3331,3401,3476,3564,3634,3734,3833,3907,3983,4090,4144,4217,4308,4404,4466,4530,4593,4692,4790,4882,4982,5040,5100", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,77,75,79,78,78,99,111,79,64,93,69,61,86,62,64,58,64,60,56,118,57,60,56,70,129,85,77,137,74,70,96,54,55,65,79,89,85,78,76,69,74,87,69,99,98,73,75,106,53,72,90,95,61,63,62,98,97,91,99,57,59,82", "endOffsets": "264,342,418,498,577,656,756,868,948,1013,1107,1177,1239,1326,1389,1454,1513,1578,1639,1696,1815,1873,1934,1991,2062,2192,2278,2356,2494,2569,2640,2737,2792,2848,2914,2994,3084,3170,3249,3326,3396,3471,3559,3629,3729,3828,3902,3978,4085,4139,4212,4303,4399,4461,4525,4588,4687,4785,4877,4977,5035,5095,5178"}, "to": {"startLines": "2,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,96", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3007,3085,3161,3241,3320,3399,3499,3611,3691,3756,3850,3920,3982,4069,4132,4197,4256,4321,4382,4439,4558,4616,4677,4734,4805,4935,5021,5099,5237,5312,5383,5480,5535,5591,5657,5737,5827,5913,5992,6069,6139,6214,6302,6372,6472,6571,6645,6721,6828,6882,6955,7046,7142,7204,7268,7331,7430,7528,7620,7720,7778,8048", "endLines": "5,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,96", "endColumns": "12,77,75,79,78,78,99,111,79,64,93,69,61,86,62,64,58,64,60,56,118,57,60,56,70,129,85,77,137,74,70,96,54,55,65,79,89,85,78,76,69,74,87,69,99,98,73,75,106,53,72,90,95,61,63,62,98,97,91,99,57,59,82", "endOffsets": "314,3080,3156,3236,3315,3394,3494,3606,3686,3751,3845,3915,3977,4064,4127,4192,4251,4316,4377,4434,4553,4611,4672,4729,4800,4930,5016,5094,5232,5307,5378,5475,5530,5586,5652,5732,5822,5908,5987,6064,6134,6209,6297,6367,6467,6566,6640,6716,6823,6877,6950,7041,7137,7199,7263,7326,7425,7523,7615,7715,7773,7833,8126"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\d9062e4afabf79b168ef61153fb05959\\transformed\\navigation-ui-2.5.3\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,160", "endColumns": "104,104", "endOffsets": "155,260"}, "to": {"startLines": "94,95", "startColumns": "4,4", "startOffsets": "7838,7943", "endColumns": "104,104", "endOffsets": "7938,8043"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\fc452d62a030469530b05e216b4ffb58\\transformed\\core-1.9.0\\res\\values-km\\values-km.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "98", "startColumns": "4", "startOffsets": "8215", "endColumns": "100", "endOffsets": "8311"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\f9b30dd2edbe8ce1c4f414282f3ca90a\\transformed\\appcompat-1.6.1\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,207,306,416,503,606,727,805,881,972,1065,1157,1251,1351,1444,1539,1633,1724,1815,1898,2002,2106,2206,2315,2424,2533,2695,2793", "endColumns": "101,98,109,86,102,120,77,75,90,92,91,93,99,92,94,93,90,90,82,103,103,99,108,108,108,161,97,83", "endOffsets": "202,301,411,498,601,722,800,876,967,1060,1152,1246,1346,1439,1534,1628,1719,1810,1893,1997,2101,2201,2310,2419,2528,2690,2788,2872"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,97", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "319,421,520,630,717,820,941,1019,1095,1186,1279,1371,1465,1565,1658,1753,1847,1938,2029,2112,2216,2320,2420,2529,2638,2747,2909,8131", "endColumns": "101,98,109,86,102,120,77,75,90,92,91,93,99,92,94,93,90,90,82,103,103,99,108,108,108,161,97,83", "endOffsets": "416,515,625,712,815,936,1014,1090,1181,1274,1366,1460,1560,1653,1748,1842,1933,2024,2107,2211,2315,2415,2524,2633,2742,2904,3002,8210"}}]}]}