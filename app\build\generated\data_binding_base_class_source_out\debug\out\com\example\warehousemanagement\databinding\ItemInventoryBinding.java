// Generated by view binder compiler. Do not edit!
package com.example.warehousemanagement.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.warehousemanagement.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemInventoryBinding implements ViewBinding {
  @NonNull
  private final CardView rootView;

  @NonNull
  public final Button btnView;

  @NonNull
  public final TextView tvCode;

  @NonNull
  public final TextView tvDate;

  private ItemInventoryBinding(@NonNull CardView rootView, @NonNull Button btnView,
      @NonNull TextView tvCode, @NonNull TextView tvDate) {
    this.rootView = rootView;
    this.btnView = btnView;
    this.tvCode = tvCode;
    this.tvDate = tvDate;
  }

  @Override
  @NonNull
  public CardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemInventoryBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemInventoryBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_inventory, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemInventoryBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnView;
      Button btnView = ViewBindings.findChildViewById(rootView, id);
      if (btnView == null) {
        break missingId;
      }

      id = R.id.tvCode;
      TextView tvCode = ViewBindings.findChildViewById(rootView, id);
      if (tvCode == null) {
        break missingId;
      }

      id = R.id.tvDate;
      TextView tvDate = ViewBindings.findChildViewById(rootView, id);
      if (tvDate == null) {
        break missingId;
      }

      return new ItemInventoryBinding((CardView) rootView, btnView, tvCode, tvDate);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
