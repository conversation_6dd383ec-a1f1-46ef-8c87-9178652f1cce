[{"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.9\\com.example.warehousemanagement.app-merged_res-50:\\mipmap-hdpi_ic_launcher.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.9\\com.example.warehousemanagement.app-main-52:\\mipmap-hdpi\\ic_launcher.png"}, {"merged": "com.example.warehousemanagement.app-merged_res-50:/mipmap-hdpi_ic_launcher_round.png.flat", "source": "com.example.warehousemanagement.app-main-52:/mipmap-hdpi/ic_launcher_round.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.9\\com.example.warehousemanagement.app-merged_res-50:\\layout_activity_details.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.9\\com.example.warehousemanagement.app-main-52:\\layout\\activity_details.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.9\\com.example.warehousemanagement.app-merged_res-50:\\layout_item_inventory.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.9\\com.example.warehousemanagement.app-main-52:\\layout\\item_inventory.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.9\\com.example.warehousemanagement.app-merged_res-50:\\layout_activity_main.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.9\\com.example.warehousemanagement.app-main-52:\\layout\\activity_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.9\\com.example.warehousemanagement.app-merged_res-50:\\layout_item_detail.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.9\\com.example.warehousemanagement.app-main-52:\\layout\\item_detail.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.9\\com.example.warehousemanagement.app-merged_res-50:\\layout_activity_inventory.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.9\\com.example.warehousemanagement.app-main-52:\\layout\\activity_inventory.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.9\\com.example.warehousemanagement.app-merged_res-50:\\menu_bottom_nav_menu.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.9\\com.example.warehousemanagement.app-main-52:\\menu\\bottom_nav_menu.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.9\\com.example.warehousemanagement.app-merged_res-50:\\layout_activity_login.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.9\\com.example.warehousemanagement.app-main-52:\\layout\\activity_login.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.9\\com.example.warehousemanagement.app-merged_res-50:\\layout_activity_form.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.9\\com.example.warehousemanagement.app-main-52:\\layout\\activity_form.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.9\\com.example.warehousemanagement.app-merged_res-50:\\drawable_bg_date_selector.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.9\\com.example.warehousemanagement.app-main-52:\\drawable\\bg_date_selector.xml"}]