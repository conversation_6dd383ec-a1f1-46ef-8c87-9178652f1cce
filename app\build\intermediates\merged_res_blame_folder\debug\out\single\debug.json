[{"merged": "com.example.warehousemanagement.app-merged_res-50:/drawable_bg_date_selector.xml.flat", "source": "com.example.warehousemanagement.app-main-52:/drawable/bg_date_selector.xml"}, {"merged": "com.example.warehousemanagement.app-merged_res-50:/mipmap-hdpi_ic_launcher.png.flat", "source": "com.example.warehousemanagement.app-main-52:/mipmap-hdpi/ic_launcher.png"}, {"merged": "com.example.warehousemanagement.app-merged_res-50:/drawable_ic_recycle.xml.flat", "source": "com.example.warehousemanagement.app-main-52:/drawable/ic_recycle.xml"}, {"merged": "com.example.warehousemanagement.app-merged_res-50:/layout_item_detail.xml.flat", "source": "com.example.warehousemanagement.app-main-52:/layout/item_detail.xml"}, {"merged": "com.example.warehousemanagement.app-merged_res-50:/layout_activity_form.xml.flat", "source": "com.example.warehousemanagement.app-main-52:/layout/activity_form.xml"}, {"merged": "com.example.warehousemanagement.app-merged_res-50:/mipmap-hdpi_ic_launcher_round.png.flat", "source": "com.example.warehousemanagement.app-main-52:/mipmap-hdpi/ic_launcher_round.png"}, {"merged": "com.example.warehousemanagement.app-merged_res-50:/drawable_ic_search.xml.flat", "source": "com.example.warehousemanagement.app-main-52:/drawable/ic_search.xml"}, {"merged": "com.example.warehousemanagement.app-merged_res-50:/drawable_ic_report.xml.flat", "source": "com.example.warehousemanagement.app-main-52:/drawable/ic_report.xml"}, {"merged": "com.example.warehousemanagement.app-merged_res-50:/mipmap-mdpi_ic_launcher.png.flat", "source": "com.example.warehousemanagement.app-main-52:/mipmap-mdpi/ic_launcher.png"}, {"merged": "com.example.warehousemanagement.app-merged_res-50:/drawable_ic_in.xml.flat", "source": "com.example.warehousemanagement.app-main-52:/drawable/ic_in.xml"}, {"merged": "com.example.warehousemanagement.app-merged_res-50:/drawable_logo.xml.flat", "source": "com.example.warehousemanagement.app-main-52:/drawable/logo.xml"}, {"merged": "com.example.warehousemanagement.app-merged_res-50:/layout_activity_login.xml.flat", "source": "com.example.warehousemanagement.app-main-52:/layout/activity_login.xml"}, {"merged": "com.example.warehousemanagement.app-merged_res-50:/menu_bottom_nav_menu.xml.flat", "source": "com.example.warehousemanagement.app-main-52:/menu/bottom_nav_menu.xml"}, {"merged": "com.example.warehousemanagement.app-merged_res-50:/layout_activity_inventory.xml.flat", "source": "com.example.warehousemanagement.app-main-52:/layout/activity_inventory.xml"}, {"merged": "com.example.warehousemanagement.app-merged_res-50:/layout_activity_details.xml.flat", "source": "com.example.warehousemanagement.app-main-52:/layout/activity_details.xml"}, {"merged": "com.example.warehousemanagement.app-merged_res-50:/layout_activity_main.xml.flat", "source": "com.example.warehousemanagement.app-main-52:/layout/activity_main.xml"}, {"merged": "com.example.warehousemanagement.app-merged_res-50:/layout_item_inventory.xml.flat", "source": "com.example.warehousemanagement.app-main-52:/layout/item_inventory.xml"}, {"merged": "com.example.warehousemanagement.app-merged_res-50:/drawable_ic_query.xml.flat", "source": "com.example.warehousemanagement.app-main-52:/drawable/ic_query.xml"}, {"merged": "com.example.warehousemanagement.app-merged_res-50:/mipmap-mdpi_ic_launcher_round.png.flat", "source": "com.example.warehousemanagement.app-main-52:/mipmap-mdpi/ic_launcher_round.png"}]